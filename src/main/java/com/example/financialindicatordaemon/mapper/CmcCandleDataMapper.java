package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.entity.CandleData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface CmcCandleDataMapper {

    long countByDateAndCurrency(@Param("coinId") Integer coinId,
                                @Param("date") LocalDate date,
                                @Param("currencyType") String currencyType);

    List<CandleData> findLatestByCoinAndCurrency(@Param("coinId") Integer coinId,
                                                 @Param("currencyType") String currencyType);

    List<CandleData> findByDateAndCurrency(@Param("coinId") Integer coinId,
                                           @Param("date") LocalDate date,
                                           @Param("currencyType") String currencyType);

    List<Integer> findDistinctCryptocurrencyIds(@Param("currencyType") String currencyType);

    void insert(@Param("candleData") CandleData candleData);

}
