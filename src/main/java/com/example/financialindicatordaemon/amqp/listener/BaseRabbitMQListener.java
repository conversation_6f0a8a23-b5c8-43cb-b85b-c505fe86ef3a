package com.example.financialindicatordaemon.amqp.listener;

import com.example.financialindicatordaemon.dto.SymbolsMessage;
import com.example.financialindicatordaemon.service.DataMiningService;
import com.example.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

import java.io.IOException;

public abstract class BaseRabbitMQListener {

    private static final Logger logger = LoggerFactory.getLogger(BaseRabbitMQListener.class);

    protected final DataMiningService dataMiningService;
    protected final DataTransformationService dataTransformationService;

    public BaseRabbitMQListener(DataMiningService dataMiningService,
                                DataTransformationService dataTransformationService) {
        this.dataMiningService = dataMiningService;
        this.dataTransformationService = dataTransformationService;
    }

    protected void handleSymbolsMessage(SymbolsMessage message, String currency,
                                        Channel channel, long deliveryTag, Message amqpMessage) {
        int payloadSize = amqpMessage.getBody().length;
        logger.info("Received message with delivery_tag: {}, payload size: {} bytes", deliveryTag, payloadSize);
        long startTime = System.currentTimeMillis();

        try {
            if (message.getSymbols() != null && !message.getSymbols().isEmpty()) {
                // Acknowledge message first
                channel.basicAck(deliveryTag, false);

                logger.info("🔄 Starting {} data mining for {} symbols: {}",
                        currency.toUpperCase(), message.getSymbols().size(), message.getSymbols());

                dataMiningService.processSymbolsForCurrency(message.getSymbols(), 2781); // Assuming 2781 is a relevant constant or should be configurable

                long processingTime = System.currentTimeMillis() - startTime;
                logger.info("Message processed successfully in {}ms (delivery_tag: {})", processingTime, deliveryTag);
            } else {
                logger.warn("Received empty or null symbols list, acknowledging message");
                channel.basicAck(deliveryTag, false);
            }

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("Message processing failed after {}ms (delivery_tag: {})", processingTime, deliveryTag, e);

            try {
                channel.basicReject(deliveryTag, false); // Don't requeue
            } catch (IOException ioException) {
                logger.error("Failed to reject message", ioException);
            }
        }
    }

    protected void handleSimpleMessage(String messageType, Channel channel, long deliveryTag,
                                       Message amqpMessage, Runnable processor) {
        int payloadSize = amqpMessage.getBody().length;
        logger.info("Received {} message with delivery_tag: {}, payload size: {} bytes",
                messageType, deliveryTag, payloadSize);
        long startTime = System.currentTimeMillis();

        try {
            // Acknowledge message first
            channel.basicAck(deliveryTag, false);

            // Process the message
            processor.run();

            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("{} message processed successfully in {}ms (delivery_tag: {})",
                    messageType, processingTime, deliveryTag);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            logger.error("{} message processing failed after {}ms (delivery_tag: {})",
                    messageType, processingTime, deliveryTag, e);

            try {
                channel.basicReject(deliveryTag, false); // Don't requeue
            } catch (IOException ioException) {
                logger.error("Failed to reject message", ioException);
            }
        }
    }
}

