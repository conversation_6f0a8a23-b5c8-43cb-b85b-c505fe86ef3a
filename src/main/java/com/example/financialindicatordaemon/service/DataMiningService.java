package com.example.financialindicatordaemon.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class DataMiningService {

    private static final Logger logger = LoggerFactory.getLogger(DataMiningService.class);

    private final CoinMarketCapService coinMarketCapService;
    private final CmcMappingsMappingService cmcMappingsMappingService;

    public DataMiningService(CoinMarketCapService coinMarketCapService, CmcMappingsMappingService cmcMappingsMappingService) {
        this.coinMarketCapService = coinMarketCapService;
        this.cmcMappingsMappingService = cmcMappingsMappingService;
    }

    public void mineMappings() {
        logger.info("Starting cryptocurrency mappings retrieval");
        if (cmcMappingsMappingService.doMappingsExist()) {
            logger.info("Mappings already exist, skipping download");
            return;
        }

        cmcMappingsMappingService.insert(coinMarketCapService.getCryptocurrencyMappings());
        logger.info("Completed cryptocurrency mappings retrieval");
    }

    public void processSymbolsForCurrency(List<String> symbols, Integer currencyId) {
        symbols.stream()
                .map(cmcMappingsMappingService::findBySymbol)
                .filter(Optional::isPresent)
                .map(id -> id.get().id())
                .map(id -> coinMarketCapService.findHistoricalQuotes(id, currencyId, null, null));
    }

}
