<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.financialindicatordaemon.mapper.CmcCandleDataMapper">
    <insert id="insert">
        INSERT INTO crypto_data.cmc_candle_data
        (cryptocurrency_id, timestamp, currency_type, open_price, high_price, low_price, close_price, volume,
         market_cap, created_at)
        VALUES (#{candleData.cryptocurrencyId}, #{candleData.timestamp}, #{candleData.currencyType},
                #{candleData.openPrice}, #{candleData.highPrice}, #{candleData.lowPrice}, #{candleData.closePrice},
                #{candleData.volume}, #{candleData.marketCap}, NOW())
        ON CONFLICT (cryptocurrency_id, timestamp, currency_type) DO UPDATE SET open_price = excluded.open_price
    </insert>

    <select id="countByDateAndCurrency" resultType="long">
        SELECT COUNT(*)
        FROM crypto_data.cmc_candle_data
        WHERE cryptocurrency_id = #{coinId}
          AND currency_type = #{currencyType}
          AND DATE(timestamp) = #{date}
    </select>

    <select id="findLatestByCoinAndCurrency" resultType="com.example.financialindicatordaemon.entity.CandleData">
        SELECT *
        FROM crypto_data.cmc_candle_data
        WHERE cryptocurrency_id = #{coinId}
          AND currency_type = #{currencyType}
        ORDER BY timestamp DESC
    </select>

    <select id="findByDateAndCurrency" resultType="com.example.financialindicatordaemon.entity.CandleData">
        SELECT *
        FROM crypto_data.cmc_candle_data
        WHERE cryptocurrency_id = #{coinId}
          AND currency_type = #{currencyType}
          AND DATE(timestamp) = #{date}
        ORDER BY timestamp ASC
    </select>

    <select id="findDistinctCryptocurrencyIds" resultType="java.lang.Integer">
        SELECT DISTINCT cryptocurrency_id
        FROM crypto_data.cmc_candle_data
        WHERE currency_type = #{currencyType}
    </select>

</mapper>
