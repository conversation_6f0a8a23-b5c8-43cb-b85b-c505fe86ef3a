package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.BaseTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertTrue;

public class DataMiningServiceTest extends BaseTest {

    @Autowired
    private DataMiningService dataMiningService;
    @Autowired
    private CmcMappingsMappingService cmcMappingsMappingService;

    @Test
    public void mineMappings_shouldStore() {
        assertTrue(cmcMappingsMappingService.findBySymbol("BTC").isEmpty());
        dataMiningService.mineMappings();
        assertTrue(cmcMappingsMappingService.findBySymbol("BTC").isPresent());
    }

}
