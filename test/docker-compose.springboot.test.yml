services:
  postgres:
    image: postgres:15-alpine
    container_name: financial_postgres_test
    environment:
      POSTGRES_DB: financial_indicator_db_test
      POSTGRES_USER: financial_user
      POSTGRES_PASSWORD: financial_pass
    networks:
      - docker-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U financial_user -d financial_indicator_db_test"]
      interval: 5s
      timeout: 5s
      retries: 5

  liquibase:
    image: liquibase/liquibase:4.25
    container_name: financial_liquibase_test
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ../src/main/resources/db/changelog:/liquibase/changelog
    networks:
      - docker-network
    environment:
      LIQUIBASE_COMMAND_URL: ***********************************************************
      LIQUIBASE_COMMAND_USERNAME: financial_user
      LIQUIBASE_COMMAND_PASSWORD: financial_pass
      LIQUIBASE_COMMAND_CHANGELOG_FILE: changelog/db.changelog-master.sql
      LIQUIBASE_COMMAND_DEFAULT_SCHEMA_NAME: financial_data
    command: ["liquibase", "update"]

  rabbitmq:
    image: rabbitmq:3-management
    container_name: financial_rabbitmq_test
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - ../rabbit_mq/conf/definitions.json:/etc/rabbitmq/definitions.json:ro
      - ../rabbit_mq/conf/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    networks:
      - docker-network
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      timeout: 5s
      retries: 5

  test:
    build:
      context: ..
      dockerfile: test/Dockerfile.springboot.test
    container_name: financial_springboot_test
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      liquibase:
        condition: service_completed_successfully
    networks:
      - docker-network
    environment:
      # Database Configuration
      - POSTGRES_DB=financial_indicator_db_test
      - POSTGRES_USER=financial_user
      - POSTGRES_PASSWORD=financial_pass
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432

      # RabbitMQ Configuration
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest

      # External API Configuration (test values)
      - CMC_API_KEY=test-key
      - CMC_API_THROTTLE_MIN=100
      - CMC_API_THROTTLE_MAX=200
      - CMC_SYMBOL_OVERRIDES=
      - INDICATOR_API_HOST=http://localhost:8080
      - AMOUNT_OF_COINS_TO_GET_BY_RANKING=3

      # Spring Profile
      - SPRING_PROFILES_ACTIVE=test
    command: ["./gradlew", "test", "--info"]

networks:
  docker-network:
    driver: bridge
